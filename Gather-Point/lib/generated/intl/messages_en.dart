// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("About App"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("About This Place"),
        "accessibility": MessageLookupByLibrary.simpleMessage("Accessibility"),
        "active": MessageLookupByLibrary.simpleMessage("Active"),
        "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
        "addProperty": MessageLookupByLibrary.simpleMessage("Add Property"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("Add to Favorites"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("Additional Information"),
        "airConditioning":
            MessageLookupByLibrary.simpleMessage("Air Conditioning"),
        "all": MessageLookupByLibrary.simpleMessage("All"),
        "allReviews": MessageLookupByLibrary.simpleMessage("All"),
        "amenities": MessageLookupByLibrary.simpleMessage("Amenities"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "Property booking and rental app"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("App information and version"),
        "appName": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "appearance": MessageLookupByLibrary.simpleMessage("Appearance"),
        "arabic": MessageLookupByLibrary.simpleMessage("Arabic"),
        "availableBalance":
            MessageLookupByLibrary.simpleMessage("Available Balance"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("Available Services"),
        "average": MessageLookupByLibrary.simpleMessage("Average"),
        "back": MessageLookupByLibrary.simpleMessage("Back"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("Back to Search"),
        "balcony": MessageLookupByLibrary.simpleMessage("Balcony"),
        "bankTransfer": MessageLookupByLibrary.simpleMessage("Bank Transfer"),
        "bar": MessageLookupByLibrary.simpleMessage("Bar"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("Bathrooms"),
        "beachAccess": MessageLookupByLibrary.simpleMessage("Beach Access"),
        "bedrooms": MessageLookupByLibrary.simpleMessage("Bedrooms"),
        "bio": MessageLookupByLibrary.simpleMessage("Bio"),
        "birthdate": MessageLookupByLibrary.simpleMessage("Birth Date"),
        "bookNow": MessageLookupByLibrary.simpleMessage("Book Now"),
        "bookingDate": MessageLookupByLibrary.simpleMessage("Booking Date"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("Booking Details & Policies"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("Booking fee"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("Booking Policy"),
        "bookingStatus": MessageLookupByLibrary.simpleMessage("Booking Status"),
        "bookingSummary":
            MessageLookupByLibrary.simpleMessage("Booking Summary"),
        "bookings": MessageLookupByLibrary.simpleMessage("Bookings"),
        "bookingsChart": MessageLookupByLibrary.simpleMessage("Bookings Chart"),
        "bookingsOverview":
            MessageLookupByLibrary.simpleMessage("Bookings Overview"),
        "browseReels": MessageLookupByLibrary.simpleMessage("Browse Reels"),
        "budgetFriendly":
            MessageLookupByLibrary.simpleMessage("Budget Friendly"),
        "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("Cancel Booking"),
        "cancelReservation":
            MessageLookupByLibrary.simpleMessage("Cancel Reservation"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("Cancellation Policy"),
        "cancelled": MessageLookupByLibrary.simpleMessage("Cancelled"),
        "categories": MessageLookupByLibrary.simpleMessage("Categories"),
        "checkConnection": MessageLookupByLibrary.simpleMessage(
            "Check your internet connection"),
        "checkIn": MessageLookupByLibrary.simpleMessage("Check In"),
        "checkInDate": MessageLookupByLibrary.simpleMessage("Check-in Date"),
        "checkInInstructions":
            MessageLookupByLibrary.simpleMessage("Check-in Instructions"),
        "checkInOut": MessageLookupByLibrary.simpleMessage("Check-in/out"),
        "checkOut": MessageLookupByLibrary.simpleMessage("Check Out"),
        "checkOutDate": MessageLookupByLibrary.simpleMessage("Check-out Date"),
        "cityView": MessageLookupByLibrary.simpleMessage("City View"),
        "comment": MessageLookupByLibrary.simpleMessage("Comment"),
        "commission": MessageLookupByLibrary.simpleMessage("Commission"),
        "completed": MessageLookupByLibrary.simpleMessage("Completed"),
        "confirmBooking":
            MessageLookupByLibrary.simpleMessage("Confirm Booking"),
        "confirmReservation":
            MessageLookupByLibrary.simpleMessage("Confirm Reservation"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("Confirm Submission"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to submit this property?"),
        "confirmed": MessageLookupByLibrary.simpleMessage("Confirmed"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("Confirmed Bookings"),
        "contactHost": MessageLookupByLibrary.simpleMessage("Contact Host"),
        "contactInfo":
            MessageLookupByLibrary.simpleMessage("Contact Information"),
        "createProperty":
            MessageLookupByLibrary.simpleMessage("Create Property"),
        "currentLocation":
            MessageLookupByLibrary.simpleMessage("Current Location"),
        "customizeExperience": MessageLookupByLibrary.simpleMessage(
            "Customize your app experience"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("Daily Price"),
        "darkMode": MessageLookupByLibrary.simpleMessage("Dark Mode"),
        "dates": MessageLookupByLibrary.simpleMessage("Dates"),
        "days": MessageLookupByLibrary.simpleMessage("days"),
        "description": MessageLookupByLibrary.simpleMessage("Description"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("Detecting location..."),
        "discoverMore": MessageLookupByLibrary.simpleMessage("Discover More"),
        "downloadReceipt":
            MessageLookupByLibrary.simpleMessage("Download Receipt"),
        "earnings": MessageLookupByLibrary.simpleMessage("Earnings"),
        "earningsChart": MessageLookupByLibrary.simpleMessage("Earnings Chart"),
        "earningsOverview":
            MessageLookupByLibrary.simpleMessage("Earnings Overview"),
        "editProfile": MessageLookupByLibrary.simpleMessage("Edit Profile"),
        "editProperty": MessageLookupByLibrary.simpleMessage("Edit Property"),
        "email": MessageLookupByLibrary.simpleMessage("Email"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "english": MessageLookupByLibrary.simpleMessage("English"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("Enter Amount"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("Enter price"),
        "enterSearchTerm":
            MessageLookupByLibrary.simpleMessage("Enter search term..."),
        "error": MessageLookupByLibrary.simpleMessage("Error"),
        "errorFetchingInfo": MessageLookupByLibrary.simpleMessage(
            "An error occurred while fetching information"),
        "excellent": MessageLookupByLibrary.simpleMessage("Excellent"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("Explore Categories"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("Explore Properties"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("Failed to create item"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("Failed to load categories"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("Failed to load facilities"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("Failed to load reels"),
        "failedToLoadVideo":
            MessageLookupByLibrary.simpleMessage("Failed to load video"),
        "fair": MessageLookupByLibrary.simpleMessage("Fair"),
        "familyFriendly":
            MessageLookupByLibrary.simpleMessage("Family Friendly"),
        "featuredPlaces":
            MessageLookupByLibrary.simpleMessage("Featured Places"),
        "female": MessageLookupByLibrary.simpleMessage("Female"),
        "filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "finalPrice": MessageLookupByLibrary.simpleMessage("Final Price"),
        "foundHelpful": MessageLookupByLibrary.simpleMessage(
            "people found this review helpful"),
        "freeParking": MessageLookupByLibrary.simpleMessage("Free Parking"),
        "freeWifi": MessageLookupByLibrary.simpleMessage("Free WiFi"),
        "fullName": MessageLookupByLibrary.simpleMessage("Full Name"),
        "garden": MessageLookupByLibrary.simpleMessage("Garden"),
        "gardenView": MessageLookupByLibrary.simpleMessage("Garden View"),
        "gatherPoint": MessageLookupByLibrary.simpleMessage("Gather Point"),
        "gender": MessageLookupByLibrary.simpleMessage("Gender"),
        "good": MessageLookupByLibrary.simpleMessage("Good"),
        "guestComment": MessageLookupByLibrary.simpleMessage("Guest Comment"),
        "guestFavorite": MessageLookupByLibrary.simpleMessage("Guest Favorite"),
        "guestName": MessageLookupByLibrary.simpleMessage("Guest Name"),
        "guestReview": MessageLookupByLibrary.simpleMessage("Guest Review"),
        "guests": MessageLookupByLibrary.simpleMessage("Guests"),
        "gym": MessageLookupByLibrary.simpleMessage("Gym"),
        "heating": MessageLookupByLibrary.simpleMessage("Heating"),
        "highRated": MessageLookupByLibrary.simpleMessage("High Rated"),
        "home": MessageLookupByLibrary.simpleMessage("Home"),
        "hostDashboard": MessageLookupByLibrary.simpleMessage("Host Dashboard"),
        "hostMode": MessageLookupByLibrary.simpleMessage("Host Mode"),
        "hostModeDescription": MessageLookupByLibrary.simpleMessage(
            "Enable host mode to manage your properties"),
        "hostName": MessageLookupByLibrary.simpleMessage("Host Name"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("Hosted by"),
        "houseRules": MessageLookupByLibrary.simpleMessage("House Rules"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("Image Gallery"),
        "importantInfo":
            MessageLookupByLibrary.simpleMessage("Important Information"),
        "inactive": MessageLookupByLibrary.simpleMessage("Inactive"),
        "instantBook": MessageLookupByLibrary.simpleMessage("Instant Book"),
        "invalidDate": MessageLookupByLibrary.simpleMessage("Invalid date"),
        "kitchen": MessageLookupByLibrary.simpleMessage("Kitchen"),
        "lakeView": MessageLookupByLibrary.simpleMessage("Lake View"),
        "language": MessageLookupByLibrary.simpleMessage("Language"),
        "last6Months": MessageLookupByLibrary.simpleMessage("Last 6 Months"),
        "lastMonth": MessageLookupByLibrary.simpleMessage("Last Month"),
        "latitude": MessageLookupByLibrary.simpleMessage("Latitude"),
        "laundry": MessageLookupByLibrary.simpleMessage("Laundry"),
        "leaveReview": MessageLookupByLibrary.simpleMessage("Leave Review"),
        "loading": MessageLookupByLibrary.simpleMessage("Loading..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("Loading reels..."),
        "location": MessageLookupByLibrary.simpleMessage("Location"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "Please enable location permission to use the app"),
        "login": MessageLookupByLibrary.simpleMessage("Login"),
        "logout": MessageLookupByLibrary.simpleMessage("Logout"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "Are you sure you want to logout?"),
        "longitude": MessageLookupByLibrary.simpleMessage("Longitude"),
        "luxuryStay": MessageLookupByLibrary.simpleMessage("Luxury Stay"),
        "male": MessageLookupByLibrary.simpleMessage("Male"),
        "manageNotifications": MessageLookupByLibrary.simpleMessage(
            "Manage notification settings"),
        "mapView": MessageLookupByLibrary.simpleMessage("Map View"),
        "minimumWithdraw":
            MessageLookupByLibrary.simpleMessage("Minimum withdrawal: \$50"),
        "modifyReservation":
            MessageLookupByLibrary.simpleMessage("Modify Reservation"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("Monthly Price"),
        "mountainView": MessageLookupByLibrary.simpleMessage("Mountain View"),
        "mustLogin": MessageLookupByLibrary.simpleMessage("You must login"),
        "mustLoginDescription": MessageLookupByLibrary.simpleMessage(
            "Please login to view your profile"),
        "muteVideo": MessageLookupByLibrary.simpleMessage("Mute Video"),
        "myBookings": MessageLookupByLibrary.simpleMessage("My Bookings"),
        "myListings": MessageLookupByLibrary.simpleMessage("My Listings"),
        "nearbyAttractions":
            MessageLookupByLibrary.simpleMessage("Nearby Attractions"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("Nearby Places"),
        "newListing": MessageLookupByLibrary.simpleMessage("New Listing"),
        "next": MessageLookupByLibrary.simpleMessage("Next"),
        "nights": MessageLookupByLibrary.simpleMessage("Nights"),
        "nightsStayed": MessageLookupByLibrary.simpleMessage("Nights Stayed"),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("No Amenities Listed"),
        "noBookingsMessage":
            MessageLookupByLibrary.simpleMessage("No recent bookings"),
        "noBookingsSubtitle": MessageLookupByLibrary.simpleMessage(
            "You haven\'t made any bookings yet"),
        "noBookingsYet":
            MessageLookupByLibrary.simpleMessage("No bookings yet"),
        "noData": MessageLookupByLibrary.simpleMessage("No data available"),
        "noDescription": MessageLookupByLibrary.simpleMessage("No description"),
        "noPropertiesSubtitle": MessageLookupByLibrary.simpleMessage(
            "Start by adding your first property"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("No properties yet"),
        "noRecentBookings":
            MessageLookupByLibrary.simpleMessage("No recent bookings"),
        "noRecentReviews":
            MessageLookupByLibrary.simpleMessage("No recent reviews"),
        "noResults": MessageLookupByLibrary.simpleMessage("No results found"),
        "noReviewsFound":
            MessageLookupByLibrary.simpleMessage("No reviews found"),
        "noReviewsMatchFilter": MessageLookupByLibrary.simpleMessage(
            "No reviews match the selected filter"),
        "noReviewsMessage":
            MessageLookupByLibrary.simpleMessage("No recent reviews"),
        "noSearchResults":
            MessageLookupByLibrary.simpleMessage("No search results"),
        "noSmoking": MessageLookupByLibrary.simpleMessage("No Smoking"),
        "noTitle": MessageLookupByLibrary.simpleMessage("No title"),
        "noView": MessageLookupByLibrary.simpleMessage("No View"),
        "normalDays": MessageLookupByLibrary.simpleMessage("Normal Days"),
        "notAvailable": MessageLookupByLibrary.simpleMessage("Not available"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("Not specified"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifications"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bathrooms"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("Number of Bedrooms"),
        "numberOfDays": MessageLookupByLibrary.simpleMessage("Number of Days"),
        "numberOfGuests":
            MessageLookupByLibrary.simpleMessage("Number of Guests"),
        "oceanView": MessageLookupByLibrary.simpleMessage("Ocean View"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "partyFriendly": MessageLookupByLibrary.simpleMessage("Party Friendly"),
        "pauseVideo": MessageLookupByLibrary.simpleMessage("Pause Video"),
        "paypal": MessageLookupByLibrary.simpleMessage("PayPal"),
        "pending": MessageLookupByLibrary.simpleMessage("Pending"),
        "pendingEarnings":
            MessageLookupByLibrary.simpleMessage("Pending Earnings"),
        "perNight": MessageLookupByLibrary.simpleMessage("per night"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("Personal Information"),
        "petFriendly": MessageLookupByLibrary.simpleMessage("Pet Friendly"),
        "phone": MessageLookupByLibrary.simpleMessage("Phone Number"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("Pick Location"),
        "playVideo": MessageLookupByLibrary.simpleMessage("Play Video"),
        "pleaseSelectBothDates":
            MessageLookupByLibrary.simpleMessage("Please select both dates"),
        "pool": MessageLookupByLibrary.simpleMessage("Pool"),
        "poor": MessageLookupByLibrary.simpleMessage("Poor"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("Popular Destinations"),
        "popularPlaces": MessageLookupByLibrary.simpleMessage("Popular Places"),
        "price": MessageLookupByLibrary.simpleMessage("Price"),
        "priceBredown": MessageLookupByLibrary.simpleMessage("Price Breakdown"),
        "priceDetails": MessageLookupByLibrary.simpleMessage("Price Details"),
        "pricePerNight":
            MessageLookupByLibrary.simpleMessage("Price per night"),
        "priceType": MessageLookupByLibrary.simpleMessage("Price Type"),
        "pricing": MessageLookupByLibrary.simpleMessage("Pricing"),
        "privacy": MessageLookupByLibrary.simpleMessage("Privacy & Security"),
        "privacySettings": MessageLookupByLibrary.simpleMessage(
            "Privacy and security settings"),
        "processing": MessageLookupByLibrary.simpleMessage("Processing"),
        "profile": MessageLookupByLibrary.simpleMessage("Profile"),
        "profileImage": MessageLookupByLibrary.simpleMessage("Profile Image"),
        "properties": MessageLookupByLibrary.simpleMessage("Properties"),
        "propertyCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
            "Property created successfully!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("Property Description"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("Property Details"),
        "propertyLocation":
            MessageLookupByLibrary.simpleMessage("Property Location"),
        "propertyName": MessageLookupByLibrary.simpleMessage("Property Name"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("Property Title"),
        "propertyType": MessageLookupByLibrary.simpleMessage("Property Type"),
        "rareFind": MessageLookupByLibrary.simpleMessage("Rare Find"),
        "rating": MessageLookupByLibrary.simpleMessage("Rating"),
        "rebookProperty":
            MessageLookupByLibrary.simpleMessage("Rebook Property"),
        "recentBookings":
            MessageLookupByLibrary.simpleMessage("Recent Bookings"),
        "recentReviews": MessageLookupByLibrary.simpleMessage("Recent"),
        "reels": MessageLookupByLibrary.simpleMessage("Reels"),
        "removeFromFavorites":
            MessageLookupByLibrary.simpleMessage("Remove from Favorites"),
        "reservationConfirmed": MessageLookupByLibrary.simpleMessage(
            "Reservation confirmed successfully!"),
        "reservationFailed": MessageLookupByLibrary.simpleMessage(
            "Failed to confirm reservation"),
        "reservationFrom":
            MessageLookupByLibrary.simpleMessage("Reservation From"),
        "reservationTo": MessageLookupByLibrary.simpleMessage("Reservation To"),
        "reservations": MessageLookupByLibrary.simpleMessage("Reservations"),
        "reserve": MessageLookupByLibrary.simpleMessage("Reserve"),
        "restaurant": MessageLookupByLibrary.simpleMessage("Restaurant"),
        "retry": MessageLookupByLibrary.simpleMessage("Retry"),
        "reviewReservation":
            MessageLookupByLibrary.simpleMessage("Review Reservation"),
        "reviews": MessageLookupByLibrary.simpleMessage("Reviews"),
        "reviewsOverview":
            MessageLookupByLibrary.simpleMessage("Reviews Overview"),
        "rooms": MessageLookupByLibrary.simpleMessage("Rooms"),
        "rules": MessageLookupByLibrary.simpleMessage("Rules"),
        "safetyFeatures":
            MessageLookupByLibrary.simpleMessage("Safety Features"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("Save Changes"),
        "search": MessageLookupByLibrary.simpleMessage("Search"),
        "searchHint": MessageLookupByLibrary.simpleMessage(
            "Search your favorite destination..."),
        "searchPlaceholder": MessageLookupByLibrary.simpleMessage(
            "Welcome... Search for what you want"),
        "searchResults": MessageLookupByLibrary.simpleMessage("Search Results"),
        "searching": MessageLookupByLibrary.simpleMessage("Searching..."),
        "seeAll": MessageLookupByLibrary.simpleMessage("See All"),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("Select Birthdate"),
        "selectCategory":
            MessageLookupByLibrary.simpleMessage("Select Category"),
        "selectCity": MessageLookupByLibrary.simpleMessage("Select City"),
        "selectReservationDate":
            MessageLookupByLibrary.simpleMessage("Select Reservation Date"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("Select available services"),
        "selectedPeriodNotAvailable": MessageLookupByLibrary.simpleMessage(
            "The selected period is not available, please choose another period."),
        "serviceFee": MessageLookupByLibrary.simpleMessage("Service fee"),
        "settings": MessageLookupByLibrary.simpleMessage("Settings"),
        "share": MessageLookupByLibrary.simpleMessage("Share"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("Share Property"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("Show All Amenities"),
        "showLess": MessageLookupByLibrary.simpleMessage("Show Less"),
        "showMore": MessageLookupByLibrary.simpleMessage("Show More"),
        "skip": MessageLookupByLibrary.simpleMessage("Skip"),
        "smokingAllowed":
            MessageLookupByLibrary.simpleMessage("Smoking Allowed"),
        "soundClick": MessageLookupByLibrary.simpleMessage("Click Sounds"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("Click sounds disabled"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("Click sounds enabled"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("Scroll Sounds"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds disabled"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("Scroll sounds enabled"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("Sound Settings"),
        "spa": MessageLookupByLibrary.simpleMessage("Spa"),
        "start": MessageLookupByLibrary.simpleMessage("Start"),
        "status": MessageLookupByLibrary.simpleMessage("Status"),
        "streetView": MessageLookupByLibrary.simpleMessage("Street View"),
        "submit": MessageLookupByLibrary.simpleMessage("Submit"),
        "superhost": MessageLookupByLibrary.simpleMessage("Superhost"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("Tap to change image"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("Tap to upload images"),
        "taxes": MessageLookupByLibrary.simpleMessage("Taxes"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("Light theme enabled"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("Dark theme enabled"),
        "thisMonth": MessageLookupByLibrary.simpleMessage("This Month"),
        "thisYear": MessageLookupByLibrary.simpleMessage("This Year"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("Title & Description"),
        "topRated": MessageLookupByLibrary.simpleMessage("Top Rated"),
        "total": MessageLookupByLibrary.simpleMessage("Total"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("Total Amount"),
        "totalBookings": MessageLookupByLibrary.simpleMessage("Total Bookings"),
        "totalEarnings": MessageLookupByLibrary.simpleMessage("Total Earnings"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("Total Price"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("Total Properties"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("Total Reservations"),
        "totalReviews": MessageLookupByLibrary.simpleMessage("Total Reviews"),
        "totalViews": MessageLookupByLibrary.simpleMessage("Total Views"),
        "totalWithdrawn":
            MessageLookupByLibrary.simpleMessage("Total Withdrawn"),
        "transportation":
            MessageLookupByLibrary.simpleMessage("Transportation"),
        "tryDifferentKeywords": MessageLookupByLibrary.simpleMessage(
            "Try searching with different keywords"),
        "tryDifferentSearch": MessageLookupByLibrary.simpleMessage(
            "Try searching with different words"),
        "tv": MessageLookupByLibrary.simpleMessage("TV"),
        "underReview": MessageLookupByLibrary.simpleMessage("Under Review"),
        "unitDetails": MessageLookupByLibrary.simpleMessage("Unit Details"),
        "unitName": MessageLookupByLibrary.simpleMessage("Unit Name"),
        "unmuteVideo": MessageLookupByLibrary.simpleMessage("Unmute Video"),
        "verified": MessageLookupByLibrary.simpleMessage("Verified"),
        "version": MessageLookupByLibrary.simpleMessage("Version"),
        "veryGood": MessageLookupByLibrary.simpleMessage("Very Good"),
        "viewAll": MessageLookupByLibrary.simpleMessage("View All"),
        "viewAllBookings":
            MessageLookupByLibrary.simpleMessage("View All Bookings"),
        "viewAllReviews":
            MessageLookupByLibrary.simpleMessage("View All Reviews"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("View Details"),
        "views": MessageLookupByLibrary.simpleMessage("Views"),
        "walletBalance": MessageLookupByLibrary.simpleMessage("Wallet Balance"),
        "weekendDays": MessageLookupByLibrary.simpleMessage("Weekend Days"),
        "weekendPrice": MessageLookupByLibrary.simpleMessage("Weekend Price"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("Weekly Price"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("What This Place Offers"),
        "whereYoullBe":
            MessageLookupByLibrary.simpleMessage("Where You\'ll Be"),
        "wifi": MessageLookupByLibrary.simpleMessage("Wi-Fi"),
        "withdraw": MessageLookupByLibrary.simpleMessage("Withdraw"),
        "withdrawFunds": MessageLookupByLibrary.simpleMessage("Withdraw Funds"),
        "withdrawalMethod":
            MessageLookupByLibrary.simpleMessage("Withdrawal Method"),
        "workFriendly": MessageLookupByLibrary.simpleMessage("Work Friendly"),
        "workspace": MessageLookupByLibrary.simpleMessage("Workspace"),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("years hosting")
      };
}
