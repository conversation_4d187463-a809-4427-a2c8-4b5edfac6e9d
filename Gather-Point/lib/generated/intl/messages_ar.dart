// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutApp": MessageLookupByLibrary.simpleMessage("حول التطبيق"),
        "aboutThisPlace":
            MessageLookupByLibrary.simpleMessage("حول هذا المكان"),
        "accessibility": MessageLookupByLibrary.simpleMessage("إمكانية الوصول"),
        "accountSecurity": MessageLookupByLibrary.simpleMessage("أمان الحساب"),
        "active": MessageLookupByLibrary.simpleMessage("نشطة"),
        "addImage": MessageLookupByLibrary.simpleMessage("إضافة صورة"),
        "addProperty": MessageLookupByLibrary.simpleMessage("إضافة عقار"),
        "addToFavorites":
            MessageLookupByLibrary.simpleMessage("أضف إلى المفضلة"),
        "additionalInfo":
            MessageLookupByLibrary.simpleMessage("معلومات إضافية"),
        "additionalSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات إضافية"),
        "airConditioning": MessageLookupByLibrary.simpleMessage("تكييف"),
        "all": MessageLookupByLibrary.simpleMessage("الكل"),
        "allCategories": MessageLookupByLibrary.simpleMessage("جميع الفئات"),
        "allReviews": MessageLookupByLibrary.simpleMessage("الكل"),
        "amenities": MessageLookupByLibrary.simpleMessage("المرافق"),
        "appDescription": MessageLookupByLibrary.simpleMessage(
            "تطبيق لحجز العقارات والأماكن"),
        "appInfo":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appInformation":
            MessageLookupByLibrary.simpleMessage("معلومات التطبيق والإصدار"),
        "appName": MessageLookupByLibrary.simpleMessage("نقطة التجمع"),
        "appearance": MessageLookupByLibrary.simpleMessage("المظهر"),
        "applyFilter": MessageLookupByLibrary.simpleMessage("تطبيق التصفية"),
        "arabic": MessageLookupByLibrary.simpleMessage("العربية"),
        "availableBalance":
            MessageLookupByLibrary.simpleMessage("الرصيد المتاح"),
        "availableServices":
            MessageLookupByLibrary.simpleMessage("الخدمات المتوفرة"),
        "average": MessageLookupByLibrary.simpleMessage("متوسط"),
        "back": MessageLookupByLibrary.simpleMessage("رجوع"),
        "backToSearch": MessageLookupByLibrary.simpleMessage("العودة للبحث"),
        "balcony": MessageLookupByLibrary.simpleMessage("شرفة"),
        "bankTransfer": MessageLookupByLibrary.simpleMessage("تحويل بنكي"),
        "bar": MessageLookupByLibrary.simpleMessage("بار"),
        "bathrooms": MessageLookupByLibrary.simpleMessage("حمامات"),
        "beachAccess": MessageLookupByLibrary.simpleMessage("وصول للشاطئ"),
        "bedrooms": MessageLookupByLibrary.simpleMessage("غرف النوم"),
        "bio": MessageLookupByLibrary.simpleMessage("الوصف"),
        "birthdate": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
        "bookNow": MessageLookupByLibrary.simpleMessage("أحجز الآن"),
        "bookingDate": MessageLookupByLibrary.simpleMessage("تاريخ الحجز"),
        "bookingDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل وسياسات الحجز"),
        "bookingFee": MessageLookupByLibrary.simpleMessage("رسوم الحجز"),
        "bookingPolicy": MessageLookupByLibrary.simpleMessage("سياسة الحجز"),
        "bookingStatus": MessageLookupByLibrary.simpleMessage("حالة الحجز"),
        "bookingSummary": MessageLookupByLibrary.simpleMessage("ملخص الحجز"),
        "bookings": MessageLookupByLibrary.simpleMessage("الحجوزات"),
        "bookingsChart": MessageLookupByLibrary.simpleMessage("مخطط الحجوزات"),
        "bookingsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على الحجوزات"),
        "browseReels": MessageLookupByLibrary.simpleMessage("تصفح الريلز"),
        "budgetFriendly":
            MessageLookupByLibrary.simpleMessage("صديق للميزانية"),
        "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancelReservation":
            MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "cancellationPolicy":
            MessageLookupByLibrary.simpleMessage("سياسة الإلغاء"),
        "cancelled": MessageLookupByLibrary.simpleMessage("ملغية"),
        "categories": MessageLookupByLibrary.simpleMessage("الفئات"),
        "changePassword":
            MessageLookupByLibrary.simpleMessage("تغيير كلمة المرور"),
        "changePasswordDesc":
            MessageLookupByLibrary.simpleMessage("تحديث كلمة مرور حسابك"),
        "checkConnection":
            MessageLookupByLibrary.simpleMessage("تحقق من اتصالك بالإنترنت"),
        "checkIn": MessageLookupByLibrary.simpleMessage("تسجيل الوصول"),
        "checkInDate":
            MessageLookupByLibrary.simpleMessage("تاريخ تسجيل الوصول"),
        "checkInInstructions":
            MessageLookupByLibrary.simpleMessage("تعليمات تسجيل الوصول"),
        "checkInOut": MessageLookupByLibrary.simpleMessage("الوصول/المغادرة"),
        "checkOut": MessageLookupByLibrary.simpleMessage("تسجيل المغادرة"),
        "checkOutDate":
            MessageLookupByLibrary.simpleMessage("تاريخ تسجيل المغادرة"),
        "cityView": MessageLookupByLibrary.simpleMessage("إطلالة المدينة"),
        "clearFilter": MessageLookupByLibrary.simpleMessage("مسح التصفية"),
        "comment": MessageLookupByLibrary.simpleMessage("تعليق"),
        "commentFailed":
            MessageLookupByLibrary.simpleMessage("فشل في نشر التعليق"),
        "commentPosted":
            MessageLookupByLibrary.simpleMessage("تم نشر التعليق بنجاح"),
        "comments": MessageLookupByLibrary.simpleMessage("التعليقات"),
        "commission": MessageLookupByLibrary.simpleMessage("العمولة"),
        "completed": MessageLookupByLibrary.simpleMessage("مكتملة"),
        "confirmBooking": MessageLookupByLibrary.simpleMessage("تأكيد الحجز"),
        "confirmReservation":
            MessageLookupByLibrary.simpleMessage("تأكيد الحجز"),
        "confirmSubmission":
            MessageLookupByLibrary.simpleMessage("تأكيد الإرسال"),
        "confirmSubmissionMessage": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد من إرسال هذا العقار؟"),
        "confirmed": MessageLookupByLibrary.simpleMessage("مؤكدة"),
        "confirmedBookings":
            MessageLookupByLibrary.simpleMessage("الحجوزات المؤكدة"),
        "connectedDevices":
            MessageLookupByLibrary.simpleMessage("الأجهزة المتصلة"),
        "connectedDevicesDesc": MessageLookupByLibrary.simpleMessage(
            "إدارة الأجهزة المتصلة بحسابك"),
        "contactHost": MessageLookupByLibrary.simpleMessage("تواصل مع المضيف"),
        "contactInfo": MessageLookupByLibrary.simpleMessage("معلومات التواصل"),
        "contactUs": MessageLookupByLibrary.simpleMessage("اتصل بنا"),
        "contactUsDesc":
            MessageLookupByLibrary.simpleMessage("تواصل مع فريق الدعم لدينا"),
        "createProperty": MessageLookupByLibrary.simpleMessage("إنشاء عقار"),
        "currentLocation":
            MessageLookupByLibrary.simpleMessage("الموقع الحالي"),
        "customizeExperience":
            MessageLookupByLibrary.simpleMessage("خصص تجربتك في التطبيق"),
        "dailyPrice": MessageLookupByLibrary.simpleMessage("السعر اليومي"),
        "darkMode": MessageLookupByLibrary.simpleMessage("الوضع المظلم"),
        "dataAndPrivacy":
            MessageLookupByLibrary.simpleMessage("البيانات والخصوصية"),
        "dataCollection": MessageLookupByLibrary.simpleMessage("جمع البيانات"),
        "dataCollectionDesc":
            MessageLookupByLibrary.simpleMessage("كيف نجمع ونستخدم بياناتك"),
        "dataRetention":
            MessageLookupByLibrary.simpleMessage("الاحتفاظ بالبيانات"),
        "dataRetentionDesc":
            MessageLookupByLibrary.simpleMessage("كم من الوقت نحتفظ ببياناتك"),
        "dates": MessageLookupByLibrary.simpleMessage("التواريخ"),
        "days": MessageLookupByLibrary.simpleMessage("أيام"),
        "deleteAccount": MessageLookupByLibrary.simpleMessage("حذف الحساب"),
        "deleteAccountDesc":
            MessageLookupByLibrary.simpleMessage("حذف حسابك وبياناتك نهائياً"),
        "deleteComment": MessageLookupByLibrary.simpleMessage("حذف التعليق"),
        "description": MessageLookupByLibrary.simpleMessage("الوصف"),
        "detectingLocation":
            MessageLookupByLibrary.simpleMessage("جارٍ تحديد المدينة..."),
        "discoverMore": MessageLookupByLibrary.simpleMessage("اكتشف المزيد"),
        "downloadReceipt":
            MessageLookupByLibrary.simpleMessage("تحميل الإيصال"),
        "earnings": MessageLookupByLibrary.simpleMessage("الأرباح"),
        "earningsChart": MessageLookupByLibrary.simpleMessage("مخطط الأرباح"),
        "earningsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على الأرباح"),
        "editComment": MessageLookupByLibrary.simpleMessage("تعديل التعليق"),
        "editProfile":
            MessageLookupByLibrary.simpleMessage("تعديل الملف الشخصي"),
        "editProperty": MessageLookupByLibrary.simpleMessage("تعديل"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "enableAllNotifications": MessageLookupByLibrary.simpleMessage(
            "تفعيل أو إلغاء جميع الإشعارات"),
        "enableHostMode": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "enableNotificationsInSettings": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل الإشعارات في إعدادات الجهاز"),
        "english": MessageLookupByLibrary.simpleMessage("الإنجليزية"),
        "enterAmount": MessageLookupByLibrary.simpleMessage("أدخل المبلغ"),
        "enterPrice": MessageLookupByLibrary.simpleMessage("أدخل السعر"),
        "enterSearchTerm":
            MessageLookupByLibrary.simpleMessage("أدخل كلمة البحث..."),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "errorFetchingInfo":
            MessageLookupByLibrary.simpleMessage("حدث خطأ أثناء جلب المعلومات"),
        "eventNotifications":
            MessageLookupByLibrary.simpleMessage("إشعارات الأحداث"),
        "excellent": MessageLookupByLibrary.simpleMessage("ممتاز"),
        "exploreCategories":
            MessageLookupByLibrary.simpleMessage("تصفح الأقسام"),
        "exploreProperties":
            MessageLookupByLibrary.simpleMessage("استكشف العقارات"),
        "failedToCreateItem":
            MessageLookupByLibrary.simpleMessage("فشل إنشاء العنصر"),
        "failedToLoadCategories":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الأقسام"),
        "failedToLoadFacilities":
            MessageLookupByLibrary.simpleMessage("فشل تحميل الخدمات"),
        "failedToLoadReels":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الريلز"),
        "failedToLoadVideo":
            MessageLookupByLibrary.simpleMessage("فشل في تحميل الفيديو"),
        "fair": MessageLookupByLibrary.simpleMessage("مقبول"),
        "familyFriendly":
            MessageLookupByLibrary.simpleMessage("مناسب للعائلات"),
        "featuredPlaces":
            MessageLookupByLibrary.simpleMessage("الأماكن المميزة"),
        "female": MessageLookupByLibrary.simpleMessage("أنثى"),
        "filter": MessageLookupByLibrary.simpleMessage("تصفية"),
        "filterReels": MessageLookupByLibrary.simpleMessage("تصفية الريلز"),
        "finalPrice": MessageLookupByLibrary.simpleMessage("السعر النهائي"),
        "foundHelpful":
            MessageLookupByLibrary.simpleMessage("شخص وجد هذا التقييم مفيداً"),
        "freeParking": MessageLookupByLibrary.simpleMessage("موقف مجاني"),
        "freeWifi": MessageLookupByLibrary.simpleMessage("واي فاي مجاني"),
        "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
        "garden": MessageLookupByLibrary.simpleMessage("حديقة"),
        "gardenView": MessageLookupByLibrary.simpleMessage("إطلالة الحديقة"),
        "gatherPoint": MessageLookupByLibrary.simpleMessage("نقطة التجمع"),
        "gender": MessageLookupByLibrary.simpleMessage("الجنس"),
        "good": MessageLookupByLibrary.simpleMessage("جيد"),
        "guestComment": MessageLookupByLibrary.simpleMessage("تعليق الضيف"),
        "guestFavorite": MessageLookupByLibrary.simpleMessage("مفضل الضيوف"),
        "guestName": MessageLookupByLibrary.simpleMessage("اسم الضيف"),
        "guestReview": MessageLookupByLibrary.simpleMessage("تقييم الضيف"),
        "guests": MessageLookupByLibrary.simpleMessage("ضيوف"),
        "gym": MessageLookupByLibrary.simpleMessage("صالة رياضية"),
        "heating": MessageLookupByLibrary.simpleMessage("تدفئة"),
        "hideComments": MessageLookupByLibrary.simpleMessage("إخفاء التعليقات"),
        "highRated": MessageLookupByLibrary.simpleMessage("عالي التقييم"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "hostDashboard": MessageLookupByLibrary.simpleMessage("لوحة المضيف"),
        "hostMode": MessageLookupByLibrary.simpleMessage("وضع المستضيف"),
        "hostModeDescription": MessageLookupByLibrary.simpleMessage(
            "قم بتفعيل وضع المستضيف لإدارة عقاراتك"),
        "hostName": MessageLookupByLibrary.simpleMessage("اسم المضيف"),
        "hostedBy": MessageLookupByLibrary.simpleMessage("مستضاف من قبل"),
        "houseRules": MessageLookupByLibrary.simpleMessage("قواعد المنزل"),
        "imageGallery": MessageLookupByLibrary.simpleMessage("معرض الصور"),
        "importantInfo": MessageLookupByLibrary.simpleMessage("معلومات مهمة"),
        "inactive": MessageLookupByLibrary.simpleMessage("غير نشطة"),
        "instantBook": MessageLookupByLibrary.simpleMessage("حجز فوري"),
        "invalidDate": MessageLookupByLibrary.simpleMessage("تاريخ غير صالح"),
        "kitchen": MessageLookupByLibrary.simpleMessage("مطبخ"),
        "lakeView": MessageLookupByLibrary.simpleMessage("إطلالة البحيرة"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "last6Months": MessageLookupByLibrary.simpleMessage("آخر 6 أشهر"),
        "lastMonth": MessageLookupByLibrary.simpleMessage("الشهر الماضي"),
        "latitude": MessageLookupByLibrary.simpleMessage("خط العرض"),
        "laundry": MessageLookupByLibrary.simpleMessage("غسيل"),
        "leaveReview": MessageLookupByLibrary.simpleMessage("اترك تقييم"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "loadingReels":
            MessageLookupByLibrary.simpleMessage("جاري تحميل الريلز..."),
        "location": MessageLookupByLibrary.simpleMessage("الموقع"),
        "locationPermissionError": MessageLookupByLibrary.simpleMessage(
            "يرجى تفعيل إذن الموقع لاستخدام التطبيق"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "loginHistory":
            MessageLookupByLibrary.simpleMessage("سجل تسجيل الدخول"),
        "loginHistoryDesc": MessageLookupByLibrary.simpleMessage(
            "عرض نشاط تسجيل الدخول الأخير"),
        "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "logoutConfirmation": MessageLookupByLibrary.simpleMessage(
            "هل أنت متأكد أنك تريد تسجيل الخروج؟"),
        "longitude": MessageLookupByLibrary.simpleMessage("خط الطول"),
        "luxuryStay": MessageLookupByLibrary.simpleMessage("إقامة فاخرة"),
        "male": MessageLookupByLibrary.simpleMessage("ذكر"),
        "manageNotifications":
            MessageLookupByLibrary.simpleMessage("إدارة تفضيلات الإشعارات"),
        "mapView": MessageLookupByLibrary.simpleMessage("عرض الخريطة"),
        "marketingNotifications":
            MessageLookupByLibrary.simpleMessage("الإشعارات التسويقية"),
        "messageNotifications":
            MessageLookupByLibrary.simpleMessage("إشعارات الرسائل"),
        "minimumWithdraw":
            MessageLookupByLibrary.simpleMessage("الحد الأدنى للسحب: 50 دولار"),
        "modifyReservation":
            MessageLookupByLibrary.simpleMessage("تعديل الحجز"),
        "monthlyPrice": MessageLookupByLibrary.simpleMessage("السعر الشهري"),
        "mostCommented": MessageLookupByLibrary.simpleMessage("الأكثر تعليقاً"),
        "mostLiked": MessageLookupByLibrary.simpleMessage("الأكثر إعجاباً"),
        "mountainView": MessageLookupByLibrary.simpleMessage("إطلالة جبلية"),
        "mustLogin":
            MessageLookupByLibrary.simpleMessage("يجب عليك تسجيل الدخول"),
        "mustLoginDescription": MessageLookupByLibrary.simpleMessage(
            "قم بتسجيل الدخول لعرض حسابك الشخصي"),
        "muteVideo": MessageLookupByLibrary.simpleMessage("كتم الصوت"),
        "myBookings": MessageLookupByLibrary.simpleMessage("حجوزاتي"),
        "myListings": MessageLookupByLibrary.simpleMessage("عقاراتي"),
        "nearbyAttractions":
            MessageLookupByLibrary.simpleMessage("المعالم القريبة"),
        "nearbyPlaces": MessageLookupByLibrary.simpleMessage("الأماكن القريبة"),
        "newEventsAndUpdates": MessageLookupByLibrary.simpleMessage(
            "إشعارات حول الأحداث الجديدة والتحديثات"),
        "newListing": MessageLookupByLibrary.simpleMessage("إعلان جديد"),
        "newMessagesAndChats": MessageLookupByLibrary.simpleMessage(
            "إشعارات الرسائل الجديدة والمحادثات"),
        "newest": MessageLookupByLibrary.simpleMessage("الأحدث"),
        "next": MessageLookupByLibrary.simpleMessage("التالي"),
        "nights": MessageLookupByLibrary.simpleMessage("ليالي"),
        "nightsStayed": MessageLookupByLibrary.simpleMessage("عدد الليالي"),
        "noAmenitiesListed":
            MessageLookupByLibrary.simpleMessage("لا توجد مرافق مدرجة"),
        "noBookingsMessage":
            MessageLookupByLibrary.simpleMessage("لا توجد حجوزات حديثة"),
        "noBookingsSubtitle":
            MessageLookupByLibrary.simpleMessage("لم تقم بأي حجوزات حتى الآن"),
        "noBookingsYet": MessageLookupByLibrary.simpleMessage("لا توجد حجوزات"),
        "noComments": MessageLookupByLibrary.simpleMessage("لا توجد تعليقات"),
        "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
        "noDescription": MessageLookupByLibrary.simpleMessage("بدون وصف"),
        "noPropertiesSubtitle":
            MessageLookupByLibrary.simpleMessage("ابدأ بإضافة عقارك الأول"),
        "noPropertiesYet":
            MessageLookupByLibrary.simpleMessage("لا توجد عقارات"),
        "noRecentBookings":
            MessageLookupByLibrary.simpleMessage("لا توجد حجوزات حديثة"),
        "noRecentReviews":
            MessageLookupByLibrary.simpleMessage("لا توجد تعليقات حديثة"),
        "noResults": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noResultsFound": MessageLookupByLibrary.simpleMessage("لا توجد نتائج"),
        "noReviewsFound":
            MessageLookupByLibrary.simpleMessage("لا توجد تقييمات"),
        "noReviewsMatchFilter": MessageLookupByLibrary.simpleMessage(
            "لا توجد تقييمات تطابق الفلتر المحدد"),
        "noReviewsMessage":
            MessageLookupByLibrary.simpleMessage("لا توجد تعليقات حديثة"),
        "noSearchResults":
            MessageLookupByLibrary.simpleMessage("لا توجد نتائج بحث"),
        "noSmoking": MessageLookupByLibrary.simpleMessage("ممنوع التدخين"),
        "noTitle": MessageLookupByLibrary.simpleMessage("بدون عنوان"),
        "noView": MessageLookupByLibrary.simpleMessage("بدون إطلالة"),
        "normalDays":
            MessageLookupByLibrary.simpleMessage("أيام العمل العادية"),
        "notAvailable": MessageLookupByLibrary.simpleMessage("غير متوفر"),
        "notSpecified": MessageLookupByLibrary.simpleMessage("غير محدد"),
        "notificationPermissionRequired":
            MessageLookupByLibrary.simpleMessage("مطلوب إذن الإشعارات"),
        "notificationSettings":
            MessageLookupByLibrary.simpleMessage("إعدادات الإشعارات"),
        "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
        "numberOfBathrooms":
            MessageLookupByLibrary.simpleMessage("عدد الحمامات"),
        "numberOfBedrooms":
            MessageLookupByLibrary.simpleMessage("عدد غرف النوم"),
        "numberOfDays": MessageLookupByLibrary.simpleMessage("عدد الأيام"),
        "numberOfGuests": MessageLookupByLibrary.simpleMessage("عدد الضيوف"),
        "oceanView": MessageLookupByLibrary.simpleMessage("إطلالة المحيط"),
        "offersAndMarketing": MessageLookupByLibrary.simpleMessage(
            "إشعارات العروض والأخبار التسويقية"),
        "ok": MessageLookupByLibrary.simpleMessage("حسنًا"),
        "oldest": MessageLookupByLibrary.simpleMessage("الأقدم"),
        "openSettings": MessageLookupByLibrary.simpleMessage("فتح الإعدادات"),
        "partyFriendly": MessageLookupByLibrary.simpleMessage("مناسب للحفلات"),
        "pauseVideo": MessageLookupByLibrary.simpleMessage("إيقاف الفيديو"),
        "paypal": MessageLookupByLibrary.simpleMessage("باي بال"),
        "pending": MessageLookupByLibrary.simpleMessage("قيد الانتظار"),
        "pendingEarnings":
            MessageLookupByLibrary.simpleMessage("الأرباح المعلقة"),
        "perNight": MessageLookupByLibrary.simpleMessage("لكل ليلة"),
        "personalInfo":
            MessageLookupByLibrary.simpleMessage("المعلومات الشخصية"),
        "petFriendly":
            MessageLookupByLibrary.simpleMessage("مناسب للحيوانات الأليفة"),
        "phone": MessageLookupByLibrary.simpleMessage("رقم الجوال"),
        "pickLocation": MessageLookupByLibrary.simpleMessage("اختر الموقع"),
        "playVideo": MessageLookupByLibrary.simpleMessage("تشغيل الفيديو"),
        "pleaseSelectBothDates":
            MessageLookupByLibrary.simpleMessage("يرجى اختيار كلا التاريخين"),
        "pool": MessageLookupByLibrary.simpleMessage("مسبح"),
        "poor": MessageLookupByLibrary.simpleMessage("ضعيف"),
        "popularDestinations":
            MessageLookupByLibrary.simpleMessage("الوجهات الشائعة"),
        "popularPlaces":
            MessageLookupByLibrary.simpleMessage("الأماكن الشائعة"),
        "postComment": MessageLookupByLibrary.simpleMessage("نشر التعليق"),
        "price": MessageLookupByLibrary.simpleMessage("السعر"),
        "priceBredown": MessageLookupByLibrary.simpleMessage("تفاصيل الأسعار"),
        "priceDetails": MessageLookupByLibrary.simpleMessage("تفاصيل السعر"),
        "pricePerNight": MessageLookupByLibrary.simpleMessage("السعر لكل ليلة"),
        "priceType": MessageLookupByLibrary.simpleMessage("نوع السعر"),
        "pricing": MessageLookupByLibrary.simpleMessage("الأسعار"),
        "privacy": MessageLookupByLibrary.simpleMessage("الخصوصية والأمان"),
        "privacyAndSecurity":
            MessageLookupByLibrary.simpleMessage("إعدادات الخصوصية والأمان"),
        "privacySettings":
            MessageLookupByLibrary.simpleMessage("الخصوصية والأمان"),
        "processing": MessageLookupByLibrary.simpleMessage("قيد المعالجة"),
        "profile": MessageLookupByLibrary.simpleMessage("حسابي"),
        "profileImage": MessageLookupByLibrary.simpleMessage("الصورة الشخصية"),
        "properties": MessageLookupByLibrary.simpleMessage("العقارات"),
        "propertyCreatedSuccessfully":
            MessageLookupByLibrary.simpleMessage("تم إنشاء العقار بنجاح!"),
        "propertyDescription":
            MessageLookupByLibrary.simpleMessage("وصف العقار"),
        "propertyDetails":
            MessageLookupByLibrary.simpleMessage("تفاصيل العقار"),
        "propertyLocation": MessageLookupByLibrary.simpleMessage("موقع العقار"),
        "propertyName": MessageLookupByLibrary.simpleMessage("اسم العقار"),
        "propertyTitle": MessageLookupByLibrary.simpleMessage("عنوان العقار"),
        "propertyType": MessageLookupByLibrary.simpleMessage("نوع العقار"),
        "pushNotifications":
            MessageLookupByLibrary.simpleMessage("الإشعارات الفورية"),
        "rareFind": MessageLookupByLibrary.simpleMessage("اكتشاف نادر"),
        "rating": MessageLookupByLibrary.simpleMessage("التقييم"),
        "rebookProperty": MessageLookupByLibrary.simpleMessage("إعادة الحجز"),
        "recentBookings": MessageLookupByLibrary.simpleMessage("أحدث الحجوزات"),
        "recentReviews": MessageLookupByLibrary.simpleMessage("الأحدث"),
        "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
        "removeFromFavorites":
            MessageLookupByLibrary.simpleMessage("إزالة من المفضلة"),
        "replyToComment":
            MessageLookupByLibrary.simpleMessage("رد على التعليق"),
        "reservationConfirmed":
            MessageLookupByLibrary.simpleMessage("تم تأكيد الحجز بنجاح!"),
        "reservationFailed":
            MessageLookupByLibrary.simpleMessage("فشل تأكيد الحجز"),
        "reservationFrom":
            MessageLookupByLibrary.simpleMessage("تاريخ الحجز من"),
        "reservationTo":
            MessageLookupByLibrary.simpleMessage("تاريخ الحجز إلى"),
        "reservations": MessageLookupByLibrary.simpleMessage("الحجوزات"),
        "reserve": MessageLookupByLibrary.simpleMessage("احجز"),
        "restaurant": MessageLookupByLibrary.simpleMessage("مطعم"),
        "retry": MessageLookupByLibrary.simpleMessage("إعادة المحاولة"),
        "reviewReservation":
            MessageLookupByLibrary.simpleMessage("مراجعة الحجز"),
        "reviews": MessageLookupByLibrary.simpleMessage("التقييمات"),
        "reviewsOverview":
            MessageLookupByLibrary.simpleMessage("نظرة عامة على التقييمات"),
        "rooms": MessageLookupByLibrary.simpleMessage("غرف"),
        "rules": MessageLookupByLibrary.simpleMessage("القواعد"),
        "safetyFeatures": MessageLookupByLibrary.simpleMessage("ميزات الأمان"),
        "saveChanges": MessageLookupByLibrary.simpleMessage("حفظ التغييرات"),
        "search": MessageLookupByLibrary.simpleMessage("البحث"),
        "searchHint": MessageLookupByLibrary.simpleMessage("ابحث عن الريلز..."),
        "searchPlaceholder":
            MessageLookupByLibrary.simpleMessage("حياك ... دور علي اللي تبيه"),
        "searchReels": MessageLookupByLibrary.simpleMessage("البحث في الريلز"),
        "searchResults": MessageLookupByLibrary.simpleMessage("نتائج البحث"),
        "searching": MessageLookupByLibrary.simpleMessage("جاري البحث..."),
        "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "selectBirthdate":
            MessageLookupByLibrary.simpleMessage("اختر تاريخ الميلاد"),
        "selectCategory": MessageLookupByLibrary.simpleMessage("اختر القسم"),
        "selectCity": MessageLookupByLibrary.simpleMessage("اختر المدينة"),
        "selectReservationDate":
            MessageLookupByLibrary.simpleMessage("اختيار تاريخ الحجز"),
        "selectServices":
            MessageLookupByLibrary.simpleMessage("اختر الخدمات المتوفرة"),
        "selectedPeriodNotAvailable": MessageLookupByLibrary.simpleMessage(
            "الفترة المحددة غير متاحة، يرجى اختيار فترة أخرى."),
        "sendTestNotification":
            MessageLookupByLibrary.simpleMessage("إرسال إشعار تجريبي"),
        "serviceFee": MessageLookupByLibrary.simpleMessage("رسوم الخدمة"),
        "settings": MessageLookupByLibrary.simpleMessage("الإعدادات"),
        "share": MessageLookupByLibrary.simpleMessage("مشاركة"),
        "shareProperty": MessageLookupByLibrary.simpleMessage("مشاركة العقار"),
        "showAllAmenities":
            MessageLookupByLibrary.simpleMessage("عرض جميع المرافق"),
        "showComments": MessageLookupByLibrary.simpleMessage("عرض التعليقات"),
        "showLess": MessageLookupByLibrary.simpleMessage("عرض أقل"),
        "showMore": MessageLookupByLibrary.simpleMessage("عرض المزيد"),
        "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
        "smokingAllowed": MessageLookupByLibrary.simpleMessage("التدخين مسموح"),
        "sortBy": MessageLookupByLibrary.simpleMessage("ترتيب حسب"),
        "soundClick": MessageLookupByLibrary.simpleMessage("صوت النقرات"),
        "soundClickDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات النقر"),
        "soundClickEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات النقر"),
        "soundScroll": MessageLookupByLibrary.simpleMessage("صوت التمرير"),
        "soundScrollDisabled":
            MessageLookupByLibrary.simpleMessage("تم إيقاف أصوات التمرير"),
        "soundScrollEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل أصوات التمرير"),
        "soundSettings": MessageLookupByLibrary.simpleMessage("إعدادات الصوت"),
        "spa": MessageLookupByLibrary.simpleMessage("سبا"),
        "start": MessageLookupByLibrary.simpleMessage("ابدأ"),
        "status": MessageLookupByLibrary.simpleMessage("الحالة"),
        "streetView": MessageLookupByLibrary.simpleMessage("إطلالة الشارع"),
        "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
        "superhost": MessageLookupByLibrary.simpleMessage("مضيف ممتاز"),
        "tapToChangeImage":
            MessageLookupByLibrary.simpleMessage("اضغط لتغيير الصورة"),
        "tapToUploadImages":
            MessageLookupByLibrary.simpleMessage("اضغط لرفع الصور"),
        "taxes": MessageLookupByLibrary.simpleMessage("الضرائب"),
        "testNotification":
            MessageLookupByLibrary.simpleMessage("إشعار تجريبي"),
        "themeDisabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع الفاتح"),
        "themeEnabled":
            MessageLookupByLibrary.simpleMessage("تم تفعيل الوضع المظلم"),
        "thirdPartySharing":
            MessageLookupByLibrary.simpleMessage("المشاركة مع الأطراف الثالثة"),
        "thirdPartySharingDesc": MessageLookupByLibrary.simpleMessage(
            "معلومات حول مشاركة البيانات مع الشركاء"),
        "thisMonth": MessageLookupByLibrary.simpleMessage("هذا الشهر"),
        "thisYear": MessageLookupByLibrary.simpleMessage("هذا العام"),
        "titleAndDescription":
            MessageLookupByLibrary.simpleMessage("العنوان والوصف"),
        "topRated": MessageLookupByLibrary.simpleMessage("الأعلى تقييماً"),
        "total": MessageLookupByLibrary.simpleMessage("الإجمالي"),
        "totalAmount": MessageLookupByLibrary.simpleMessage("المبلغ الإجمالي"),
        "totalBookings":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalEarnings": MessageLookupByLibrary.simpleMessage("إجمالي الأرباح"),
        "totalPrice": MessageLookupByLibrary.simpleMessage("إجمالي السعر"),
        "totalProperties":
            MessageLookupByLibrary.simpleMessage("إجمالي العقارات"),
        "totalReservations":
            MessageLookupByLibrary.simpleMessage("إجمالي الحجوزات"),
        "totalReviews":
            MessageLookupByLibrary.simpleMessage("إجمالي التقييمات"),
        "totalViews": MessageLookupByLibrary.simpleMessage("إجمالي المشاهدات"),
        "totalWithdrawn":
            MessageLookupByLibrary.simpleMessage("إجمالي المسحوب"),
        "transportation": MessageLookupByLibrary.simpleMessage("المواصلات"),
        "tryDifferentKeywords":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "tryDifferentSearch":
            MessageLookupByLibrary.simpleMessage("جرب البحث بكلمات مختلفة"),
        "tv": MessageLookupByLibrary.simpleMessage("تلفزيون"),
        "twoFactorAuth":
            MessageLookupByLibrary.simpleMessage("المصادقة الثنائية"),
        "twoFactorAuthDesc":
            MessageLookupByLibrary.simpleMessage("إضافة أمان إضافي لحسابك"),
        "underReview": MessageLookupByLibrary.simpleMessage("قيد المراجعة"),
        "unitDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الوحدة"),
        "unitName": MessageLookupByLibrary.simpleMessage("اسم الوحدة"),
        "unmuteVideo": MessageLookupByLibrary.simpleMessage("إلغاء كتم الصوت"),
        "verified": MessageLookupByLibrary.simpleMessage("موثق"),
        "version": MessageLookupByLibrary.simpleMessage("الإصدار"),
        "veryGood": MessageLookupByLibrary.simpleMessage("جيد جداً"),
        "viewAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
        "viewAllBookings":
            MessageLookupByLibrary.simpleMessage("عرض جميع الحجوزات"),
        "viewAllReviews":
            MessageLookupByLibrary.simpleMessage("عرض جميع التعليقات"),
        "viewDetails": MessageLookupByLibrary.simpleMessage("عرض التفاصيل"),
        "views": MessageLookupByLibrary.simpleMessage("المشاهدات"),
        "walletBalance": MessageLookupByLibrary.simpleMessage("رصيد المحفظة"),
        "weekendDays":
            MessageLookupByLibrary.simpleMessage("أيام العطلة الأسبوعية"),
        "weekendPrice":
            MessageLookupByLibrary.simpleMessage("سعر عطلة نهاية الأسبوع"),
        "weeklyPrice": MessageLookupByLibrary.simpleMessage("السعر الأسبوعي"),
        "whatThisPlaceOffers":
            MessageLookupByLibrary.simpleMessage("ما يقدمه هذا المكان"),
        "whereYoullBe": MessageLookupByLibrary.simpleMessage("أين ستكون"),
        "wifi": MessageLookupByLibrary.simpleMessage("واي فاي"),
        "withdraw": MessageLookupByLibrary.simpleMessage("سحب"),
        "withdrawFunds": MessageLookupByLibrary.simpleMessage("سحب الأموال"),
        "withdrawalMethod": MessageLookupByLibrary.simpleMessage("طريقة السحب"),
        "workFriendly": MessageLookupByLibrary.simpleMessage("مناسب للعمل"),
        "workspace": MessageLookupByLibrary.simpleMessage("مساحة عمل"),
        "writeComment": MessageLookupByLibrary.simpleMessage("اكتب تعليق..."),
        "yearsHosting": MessageLookupByLibrary.simpleMessage("سنوات الاستضافة"),
        "yourRights": MessageLookupByLibrary.simpleMessage("حقوقك"),
        "yourRightsDesc": MessageLookupByLibrary.simpleMessage(
            "حقوق الخصوصية الخاصة بك وكيفية ممارستها")
      };
}
