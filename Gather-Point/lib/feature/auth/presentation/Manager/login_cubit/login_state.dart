part of 'login_cubit.dart';

sealed class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object> get props => [];
}

final class LoginInitial extends LoginState {}

final class LoginLoadingState extends LoginState {}

final class LoginLoading extends LoginState {}

final class LoginSuccessState extends LoginState {}

final class LoginFailureState extends LoginState {
  final String errMessage;

  const LoginFailureState({required this.errMessage});
}



final class LoginGuestLoadingState extends LoginState {}

final class LoginGuestSuccessState extends LoginState {}

final class LoginGuestFailureState extends LoginState {
  final String errMessage;

  const LoginGuestFailureState({required this.errMessage});
}

// Social Login States
final class SocialLoginLoadingState extends LoginState {
  final String provider;

  const SocialLoginLoadingState({required this.provider});

  @override
  List<Object> get props => [provider];
}

final class SocialLoginSuccessState extends LoginState {
  final String provider;

  const SocialLoginSuccessState({required this.provider});

  @override
  List<Object> get props => [provider];
}

final class SocialLoginFailureState extends LoginState {
  final String errMessage;
  final String provider;

  const SocialLoginFailureState({
    required this.errMessage,
    required this.provider,
  });

  @override
  List<Object> get props => [errMessage, provider];
}

