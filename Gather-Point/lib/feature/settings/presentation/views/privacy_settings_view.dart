import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:url_launcher/url_launcher.dart';

class PrivacySettingsView extends StatelessWidget {
  const PrivacySettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();

    return EnhancedScrollablePageLayout(
      title: s.privacySettings,
      hasBottomNavigation: false,
      padding: const EdgeInsets.all(20),
      children: [
        // Header Section
        _buildHeaderSection(context, s),
        const SizedBox(height: 24),

        // Data & Privacy Section
        _buildSettingsSection(
          context,
          title: s.dataAndPrivacy,
          icon: Icons.shield_outlined,
          children: [
            _buildPrivacyTile(
              context,
              title: s.dataCollection,
              subtitle: s.dataCollectionDesc,
              icon: Icons.data_usage_outlined,
              onTap: () => _showDataCollectionDialog(context, s),
            ),
            _buildPrivacyTile(
              context,
              title: s.thirdPartySharing,
              subtitle: s.thirdPartySharingDesc,
              icon: Icons.share_outlined,
              onTap: () => _showThirdPartySharingDialog(context, s),
            ),
            _buildPrivacyTile(
              context,
              title: s.dataRetention,
              subtitle: s.dataRetentionDesc,
              icon: Icons.schedule_outlined,
              onTap: () => _showDataRetentionDialog(context, s),
            ),
            _buildPrivacyTile(
              context,
              title: s.yourRights,
              subtitle: s.yourRightsDesc,
              icon: Icons.gavel_outlined,
              onTap: () => _showYourRightsDialog(context, s),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Account Security Section
        _buildSettingsSection(
          context,
          title: s.accountSecurity,
          icon: Icons.security_outlined,
          children: [
            _buildPrivacyTile(
              context,
              title: s.changePassword,
              subtitle: s.changePasswordDesc,
              icon: Icons.lock_outline,
              onTap: () => _navigateToChangePassword(context),
            ),
            _buildPrivacyTile(
              context,
              title: s.twoFactorAuth,
              subtitle: s.twoFactorAuthDesc,
              icon: Icons.verified_user_outlined,
              onTap: () => _navigateToTwoFactorAuth(context),
            ),
            _buildPrivacyTile(
              context,
              title: s.loginHistory,
              subtitle: s.loginHistoryDesc,
              icon: Icons.history_outlined,
              onTap: () => _navigateToLoginHistory(context),
            ),
            _buildPrivacyTile(
              context,
              title: s.connectedDevices,
              subtitle: s.connectedDevicesDesc,
              icon: Icons.devices_outlined,
              onTap: () => _navigateToConnectedDevices(context),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Support & Contact Section
        _buildSettingsSection(
          context,
          title: s.contactUs,
          icon: Icons.support_agent_outlined,
          children: [
            _buildPrivacyTile(
              context,
              title: s.contactUs,
              subtitle: s.contactUsDesc,
              icon: Icons.email_outlined,
              onTap: () => _contactSupport(context),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Danger Zone
        _buildDangerSection(context, s),

        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildHeaderSection(BuildContext context, S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.accentColor.withValues(alpha: 0.1),
            context.accentColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.privacy_tip_outlined,
              color: context.accentColor,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.privacyAndSecurity,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  s.privacyAndSecurity,
                  style: AppTextStyles.font14Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 12),
          child: Row(
            children: [
              Icon(
                icon,
                color: context.accentColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: context.cardColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacyTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: context.isDarkMode
                ? Colors.grey[700]!
                : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: context.accentColor,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: AppTextStyles.font16Medium.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.font14Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios_rounded,
          color: context.secondaryTextColor,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildDangerSection(BuildContext context, S s) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.red.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.warning_outlined,
                  color: Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  s.deleteAccount,
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: Colors.red,
                  ),
                ),
              ],
            ),
          ),
          _buildPrivacyTile(
            context,
            title: s.deleteAccount,
            subtitle: s.deleteAccountDesc,
            icon: Icons.delete_forever_outlined,
            onTap: () => _showDeleteAccountDialog(context, s),
          ),
        ],
      ),
    );
  }

  // Navigation methods
  void _navigateToChangePassword(BuildContext context) {
    // TODO: Implement navigation to change password page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Change Password - Coming Soon')),
    );
  }

  void _navigateToTwoFactorAuth(BuildContext context) {
    // TODO: Implement navigation to two-factor auth page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Two-Factor Auth - Coming Soon')),
    );
  }

  void _navigateToLoginHistory(BuildContext context) {
    // TODO: Implement navigation to login history page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Login History - Coming Soon')),
    );
  }

  void _navigateToConnectedDevices(BuildContext context) {
    // TODO: Implement navigation to connected devices page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Connected Devices - Coming Soon')),
    );
  }

  void _contactSupport(BuildContext context) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=Privacy Support Request',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not open email client')),
      );
    }
  }

  // Dialog methods
  void _showDataCollectionDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Data Collection',
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'We collect the following types of data:',
                style: AppTextStyles.font16Medium.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 12),
              _buildDataPoint('• Account information (name, email, phone)', context),
              _buildDataPoint('• Usage data (app interactions, preferences)', context),
              _buildDataPoint('• Device information (device type, OS version)', context),
              _buildDataPoint('• Location data (when permission granted)', context),
              _buildDataPoint('• Booking and transaction history)', context),
              const SizedBox(height: 12),
              Text(
                'This data helps us provide and improve our services.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              s.ok,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: context.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showThirdPartySharingDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Third Party Sharing',
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'We may share data with:',
                style: AppTextStyles.font16Medium.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 12),
              _buildDataPoint('• Payment processors (for transactions)', context),
              _buildDataPoint('• Analytics services (anonymized data)', context),
              _buildDataPoint('• Customer support tools', context),
              _buildDataPoint('• Legal authorities (when required by law)', context),
              const SizedBox(height: 12),
              Text(
                'We never sell your personal data to third parties.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              s.ok,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: context.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDataRetentionDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Data Retention',
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Data retention periods:',
                style: AppTextStyles.font16Medium.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 12),
              _buildDataPoint('• Account data: Until account deletion', context),
              _buildDataPoint('• Booking history: 7 years (legal requirement)', context),
              _buildDataPoint('• Usage analytics: 2 years', context),
              _buildDataPoint('• Support conversations: 3 years', context),
              _buildDataPoint('• Marketing data: Until unsubscribe', context),
              const SizedBox(height: 12),
              Text(
                'You can request data deletion at any time.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              s.ok,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: context.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showYourRightsDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Your Rights',
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Your privacy rights include:',
                style: AppTextStyles.font16Medium.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const SizedBox(height: 12),
              _buildDataPoint('• Right to access your data', context),
              _buildDataPoint('• Right to correct inaccurate data', context),
              _buildDataPoint('• Right to delete your data', context),
              _buildDataPoint('• Right to data portability', context),
              _buildDataPoint('• Right to object to processing', context),
              _buildDataPoint('• Right to withdraw consent', context),
              const SizedBox(height: 12),
              Text(
                'Contact support to exercise these rights.',
                style: AppTextStyles.font14Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              s.ok,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: context.accentColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context, S s) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          s.deleteAccount,
          style: AppTextStyles.font18Bold.copyWith(
            color: Colors.red,
          ),
        ),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_outlined,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'This action cannot be undone. Deleting your account will:',
              style: AppTextStyles.font16Medium.copyWith(
                color: context.primaryTextColor,
              ),
            ),
            const SizedBox(height: 12),
            _buildDataPoint('• Permanently delete all your data', context),
            _buildDataPoint('• Cancel all active bookings', context),
            _buildDataPoint('• Remove access to your account', context),
            _buildDataPoint('• Delete your booking history', context),
            const SizedBox(height: 12),
            Text(
              'Are you sure you want to continue?',
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.font14SemiBold.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement account deletion
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Account deletion - Coming Soon')),
              );
            },
            child: Text(
              s.deleteAccount,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataPoint(String text, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: AppTextStyles.font14Regular.copyWith(
          color: context.primaryTextColor,
        ),
      ),
    );
  }
}
