import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/managers/settings_cubit/settings_cubit.dart';
import 'package:gather_point/core/theme/theme_cubit.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/feature/notifications/presentation/views/notification_settings_view.dart';
import 'package:gather_point/feature/settings/presentation/views/privacy_settings_view.dart';
import 'package:gather_point/generated/l10n.dart';

class SettingsView extends StatelessWidget {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedScrollablePageLayout(
      title: s.settings,
      hasBottomNavigation: false, // Settings is a modal page
      padding: const EdgeInsets.all(20),
      children: [
        // Header Section
        _buildHeaderSection(context, s),
        const SizedBox(height: 24),

        // Theme Settings
        _buildSettingsSection(
          context,
          title: s.appearance,
          icon: Icons.palette_outlined,
          children: [
            _buildThemeTile(context, s),
          ],
        ),

        const SizedBox(height: 20),

        // Sound Settings
        _buildSettingsSection(
          context,
          title: s.soundSettings,
          icon: Icons.volume_up_outlined,
          children: [
            _buildSoundClickTile(context, s),
            _buildSoundScrollTile(context, s),
          ],
        ),

        const SizedBox(height: 20),

        // Language Settings
        _buildSettingsSection(
          context,
          title: s.language,
          icon: Icons.language_outlined,
          children: [
            _buildLanguageTile(context, s),
          ],
        ),

        const SizedBox(height: 20),

        // Additional Settings
        _buildSettingsSection(
          context,
          title: s.additionalSettings,
          icon: Icons.tune_rounded,
          children: [
            _buildNotificationsTile(context, s),
            _buildPrivacyTile(context, s),
            _buildAboutTile(context, s),
          ],
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildThemeTile(BuildContext context, S s) {
    return BlocBuilder<ThemeCubit, AppThemeMode>(
      builder: (context, themeMode) {
        final isDark = themeMode == AppThemeMode.dark;
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: context.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: SwitchListTile(
            title: Text(
              s.darkMode,
              style: AppTextStyles.font16Medium.copyWith(
                color: context.primaryTextColor
              )
            ),
            subtitle: Text(
              isDark ? s.themeEnabled : s.themeDisabled,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor
              ),
            ),
            secondary: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isDark ? Icons.dark_mode : Icons.light_mode,
                color: context.accentColor,
                size: 24,
              ),
            ),
            value: isDark,
            activeColor: context.accentColor,
            onChanged: (value) => context.read<ThemeCubit>().toggleTheme(),
          ),
        );
      },
    );
  }

  Widget _buildSoundClickTile(BuildContext context, S s) {
    return BlocBuilder<SettingsCubit, SettingsState>(
      buildWhen: (previous, current) => previous.soundClick != current.soundClick,
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: context.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: SwitchListTile(
            title: Text(
              s.soundClick,
              style: AppTextStyles.font16Medium.copyWith(
                color: context.primaryTextColor
              )
            ),
            subtitle: Text(
              state.soundClick ? s.soundClickEnabled : s.soundClickDisabled,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor
              ),
            ),
            secondary: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                state.soundClick ? Icons.volume_up : Icons.volume_off,
                color: context.accentColor,
                size: 24,
              ),
            ),
            value: state.soundClick,
            activeColor: context.accentColor,
            onChanged: (value) => context.read<SettingsCubit>().toggleSoundClick(value),
          ),
        );
      },
    );
  }

  Widget _buildSoundScrollTile(BuildContext context, S s) {
    return BlocBuilder<SettingsCubit, SettingsState>(
      buildWhen: (previous, current) => previous.soundScroll != current.soundScroll,
      builder: (context, state) {
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: context.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: SwitchListTile(
            title: Text(
              s.soundScroll,
              style: AppTextStyles.font16Medium.copyWith(
                color: context.primaryTextColor
              )
            ),
            subtitle: Text(
              state.soundScroll ? s.soundScrollEnabled : s.soundScrollDisabled,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor
              ),
            ),
            secondary: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                state.soundScroll ? Icons.music_note : Icons.music_off,
                color: context.accentColor,
                size: 24,
              ),
            ),
            value: state.soundScroll,
            activeColor: context.accentColor,
            onChanged: (value) => context.read<SettingsCubit>().toggleSoundScroll(value),
          ),
        );
      },
    );
  }

  Widget _buildLanguageTile(BuildContext context, S s) {
    return BlocBuilder<LocaleCubit, LocaleState>(
      builder: (context, state) {
        final isArabic = (state as SelectedLocale).locale.languageCode == 'ar';
        return Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          decoration: BoxDecoration(
            color: context.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.grey[700]!
                  : Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.language,
                color: context.accentColor,
                size: 24,
              ),
            ),
            title: Text(
              s.language,
              style: AppTextStyles.font16Medium.copyWith(
                color: context.primaryTextColor
              )
            ),
            subtitle: Text(
              isArabic ? s.arabic : s.english,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor
              ),
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: context.accentColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: context.accentColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: DropdownButton<String>(
                value: isArabic ? 'ar' : 'en',
                underline: const SizedBox(),
                icon: Icon(
                  Icons.keyboard_arrow_down,
                  color: context.accentColor,
                  size: 20,
                ),
                style: AppTextStyles.font14Medium.copyWith(
                  color: context.primaryTextColor
                ),
                dropdownColor: context.cardColor,
                items: [
                  DropdownMenuItem(
                    value: 'ar',
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text('🇸🇦', style: TextStyle(fontSize: 16)),
                        const SizedBox(width: 8),
                        Text(
                          s.arabic,
                          style: AppTextStyles.font14Regular.copyWith(
                            color: context.primaryTextColor
                          )
                        ),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'en',
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text('🇺🇸', style: TextStyle(fontSize: 16)),
                        const SizedBox(width: 8),
                        Text(
                          s.english,
                          style: AppTextStyles.font14Regular.copyWith(
                            color: context.primaryTextColor
                          )
                        ),
                      ],
                    ),
                  ),
                ],
                onChanged: (value) {
                  if (value == 'ar') {
                    context.read<LocaleCubit>().toArabic();
                  } else {
                    context.read<LocaleCubit>().toEnglish();
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  /// Header section with user info and theme toggle
  Widget _buildHeaderSection(BuildContext context, S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: context.cardShadow,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.settings_outlined,
                  color: context.accentColor,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      s.settings,
                      style: AppTextStyles.font20Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      s.customizeExperience,
                      style: AppTextStyles.font14Regular.copyWith(
                        color: context.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Settings section with title and children
  Widget _buildSettingsSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: context.accentColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: context.accentColor,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTextStyles.font16Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildNotificationsTile(BuildContext context, S s) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.isDarkMode
              ? Colors.grey[700]!
              : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.notifications_outlined,
            color: context.accentColor,
            size: 24,
          ),
        ),
        title: Text(
          s.notificationSettings,
          style: AppTextStyles.font16Medium.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        subtitle: Text(
          s.manageNotifications,
          style: AppTextStyles.font14Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios_rounded,
          color: context.secondaryTextColor,
          size: 16,
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const NotificationSettingsView(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPrivacyTile(BuildContext context, S s) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.isDarkMode
              ? Colors.grey[700]!
              : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.privacy_tip_outlined,
            color: context.accentColor,
            size: 24,
          ),
        ),
        title: Text(
          s.privacySettings,
          style: AppTextStyles.font16Medium.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        subtitle: Text(
          s.privacyAndSecurity,
          style: AppTextStyles.font14Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios_rounded,
          color: context.secondaryTextColor,
          size: 16,
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PrivacySettingsView(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAboutTile(BuildContext context, S s) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.isDarkMode
              ? Colors.grey[700]!
              : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.info_outline_rounded,
            color: context.accentColor,
            size: 24,
          ),
        ),
        title: Text(
          s.aboutApp,
          style: AppTextStyles.font16Medium.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        subtitle: Text(
          s.appInformation,
          style: AppTextStyles.font14Regular.copyWith(
            color: context.secondaryTextColor,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios_rounded,
          color: context.secondaryTextColor,
          size: 16,
        ),
        onTap: () {
          // Show about dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(
                s.appName,
                style: AppTextStyles.font18Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${s.version}: 1.0.0',
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    s.appDescription,
                    style: AppTextStyles.font14Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    s.ok,
                    style: AppTextStyles.font14SemiBold.copyWith(
                      color: context.accentColor,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}