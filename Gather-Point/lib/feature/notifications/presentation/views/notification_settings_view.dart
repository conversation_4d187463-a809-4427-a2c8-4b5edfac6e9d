import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:gather_point/core/services/fcm_service.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/notification_widgets.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationSettingsView extends StatefulWidget {
  const NotificationSettingsView({super.key});

  @override
  State<NotificationSettingsView> createState() => _NotificationSettingsViewState();
}

class _NotificationSettingsViewState extends State<NotificationSettingsView> {
  bool _pushNotificationsEnabled = true;
  bool _eventNotificationsEnabled = true;
  bool _messageNotificationsEnabled = true;
  bool _marketingNotificationsEnabled = false;
  String? _fcmToken;
  NotificationSettings? _notificationSettings;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _loadFCMToken();
    _checkNotificationPermissions();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _pushNotificationsEnabled = prefs.getBool('push_notifications') ?? true;
      _eventNotificationsEnabled = prefs.getBool('event_notifications') ?? true;
      _messageNotificationsEnabled = prefs.getBool('message_notifications') ?? true;
      _marketingNotificationsEnabled = prefs.getBool('marketing_notifications') ?? false;
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('push_notifications', _pushNotificationsEnabled);
    await prefs.setBool('event_notifications', _eventNotificationsEnabled);
    await prefs.setBool('message_notifications', _messageNotificationsEnabled);
    await prefs.setBool('marketing_notifications', _marketingNotificationsEnabled);
  }

  Future<void> _loadFCMToken() async {
    final token = await FCMService.getCurrentToken();
    setState(() {
      _fcmToken = token;
    });
  }

  Future<void> _checkNotificationPermissions() async {
    final messaging = FirebaseMessaging.instance;
    final settings = await messaging.getNotificationSettings();
    setState(() {
      _notificationSettings = settings;
    });
  }

  Future<void> _requestPermissions() async {
    final messaging = FirebaseMessaging.instance;
    final settings = await messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
    setState(() {
      _notificationSettings = settings;
    });
  }

  Future<void> _testNotification() async {
    if (_fcmToken != null) {
      // Show a test notification using the overlay
      final testNotification = const RemoteNotification(
        title: 'إشعار تجريبي',
        body: 'هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح',
      );
      
      NotificationOverlay.show(
        context,
        testNotification,
        duration: const Duration(seconds: 3),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppColors.black : AppColors.white,
      appBar: AppBar(
        backgroundColor: isDark ? AppColors.black : AppColors.white,
        elevation: 0,
        title: Text(
          s.notificationSettings,
          style: AppTextStyles.font18Bold.copyWith(
            color: isDark ? AppColors.white : AppColors.black,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios,
            color: isDark ? AppColors.white : AppColors.black,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Permission Status Card
            _buildPermissionStatusCard(context, s),
            const SizedBox(height: 24),

            // Notification Settings
            _buildSectionTitle(s.notificationSettings, context),
            const SizedBox(height: 16),
            _buildSettingTile(
              context: context,
              title: s.pushNotifications,
              subtitle: s.enableAllNotifications,
              value: _pushNotificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _pushNotificationsEnabled = value;
                });
                _saveSettings();
              },
            ),
            _buildSettingTile(
              context: context,
              title: s.eventNotifications,
              subtitle: s.newEventsAndUpdates,
              value: _eventNotificationsEnabled,
              onChanged: _pushNotificationsEnabled ? (value) {
                setState(() {
                  _eventNotificationsEnabled = value;
                });
                _saveSettings();
              } : null,
            ),
            _buildSettingTile(
              context: context,
              title: s.messageNotifications,
              subtitle: s.newMessagesAndChats,
              value: _messageNotificationsEnabled,
              onChanged: _pushNotificationsEnabled ? (value) {
                setState(() {
                  _messageNotificationsEnabled = value;
                });
                _saveSettings();
              } : null,
            ),
            _buildSettingTile(
              context: context,
              title: s.marketingNotifications,
              subtitle: s.offersAndMarketing,
              value: _marketingNotificationsEnabled,
              onChanged: _pushNotificationsEnabled ? (value) {
                setState(() {
                  _marketingNotificationsEnabled = value;
                });
                _saveSettings();
              } : null,
            ),

            const SizedBox(height: 24),

            // Test Notification Button
            _buildTestButton(context, s),
            
            const SizedBox(height: 24),
            
            // FCM Token Info (Debug)
            if (_fcmToken != null) _buildTokenInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatusCard(BuildContext context, S s) {
    final isAuthorized = _notificationSettings?.authorizationStatus ==
        AuthorizationStatus.authorized;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isAuthorized ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isAuthorized ? Colors.green.withOpacity(0.3) : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isAuthorized ? Icons.check_circle : Icons.warning,
            color: isAuthorized ? Colors.green : Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isAuthorized ? 'Notifications Enabled' : 'Notifications Disabled',
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: isAuthorized ? Colors.green : Colors.orange,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isAuthorized
                      ? 'You can receive notifications normally'
                      : 'Tap to enable notifications',
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          if (!isAuthorized)
            TextButton(
              onPressed: _requestPermissions,
              child: Text(
                s.openSettings,
                style: AppTextStyles.font12SemiBold.copyWith(
                  color: context.accentColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title, BuildContext context) {
    return Text(
      title,
      style: AppTextStyles.font16SemiBold.copyWith(
        color: context.primaryTextColor,
      ),
    );
  }

  Widget _buildSettingTile({
    required BuildContext context,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font14Medium.copyWith(
                    color: onChanged != null
                        ? context.primaryTextColor
                        : context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildTestButton(BuildContext context, S s) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _testNotification,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.accentColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          s.sendTestNotification,
          style: AppTextStyles.font14SemiBold.copyWith(
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildTokenInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'FCM Token (Debug)',
            style: AppTextStyles.font12SemiBold.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _fcmToken!,
            style: AppTextStyles.font10Regular.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}
