import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

/// Service for handling social authentication (Google and Apple Sign-In)
class SocialAuthService {
  static final SocialAuthService _instance = SocialAuthService._internal();
  factory SocialAuthService() => _instance;
  SocialAuthService._internal();

  // Google Sign-In instance
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
  );

  /// Sign in with Google
  /// Returns the access token on success, null on failure or cancellation
  Future<String?> signInWithGoogle() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        // User canceled the sign-in
        debugPrint('Google Sign-In: User canceled');
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Get the access token
      final String? accessToken = googleAuth.accessToken;
      
      if (accessToken == null) {
        debugPrint('Google Sign-In: Failed to get access token');
        return null;
      }

      debugPrint('Google Sign-In: Success for ${googleUser.email}');
      return accessToken;

    } catch (error) {
      debugPrint('Google Sign-In Error: $error');
      return null;
    }
  }

  /// Sign in with Apple
  /// Returns the access token on success, null on failure or cancellation
  Future<String?> signInWithApple() async {
    try {
      // Check if Apple Sign-In is available
      if (!await SignInWithApple.isAvailable()) {
        debugPrint('Apple Sign-In: Not available on this device');
        return null;
      }

      // Generate nonce for security
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      // Request credential from Apple
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );

      // Get the identity token (this acts as our access token for Apple)
      final String? identityToken = credential.identityToken;
      
      if (identityToken == null) {
        debugPrint('Apple Sign-In: Failed to get identity token');
        return null;
      }

      debugPrint('Apple Sign-In: Success for ${credential.email ?? 'unknown'}');
      return identityToken;

    } catch (error) {
      debugPrint('Apple Sign-In Error: $error');
      return null;
    }
  }

  /// Sign out from Google
  Future<void> signOutGoogle() async {
    try {
      await _googleSignIn.signOut();
      debugPrint('Google Sign-Out: Success');
    } catch (error) {
      debugPrint('Google Sign-Out Error: $error');
    }
  }

  /// Sign out from all social providers
  Future<void> signOutAll() async {
    await signOutGoogle();
    // Apple doesn't require explicit sign-out
    debugPrint('Social Auth: Signed out from all providers');
  }

  /// Check if user is currently signed in with Google
  Future<bool> isSignedInWithGoogle() async {
    try {
      return await _googleSignIn.isSignedIn();
    } catch (error) {
      debugPrint('Google Sign-In Check Error: $error');
      return false;
    }
  }

  /// Get current Google user info (if signed in)
  GoogleSignInAccount? getCurrentGoogleUser() {
    return _googleSignIn.currentUser;
  }

  /// Generate a cryptographically secure random nonce
  String _generateNonce([int length = 32]) {
    const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
  }

  /// Returns the sha256 hash of [input] in hex notation
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Get user info from Google (if signed in)
  Map<String, dynamic>? getGoogleUserInfo() {
    final user = getCurrentGoogleUser();
    if (user == null) return null;

    return {
      'id': user.id,
      'email': user.email,
      'displayName': user.displayName,
      'photoUrl': user.photoUrl,
    };
  }

  /// Disconnect Google account (revoke access)
  Future<void> disconnectGoogle() async {
    try {
      await _googleSignIn.disconnect();
      debugPrint('Google Disconnect: Success');
    } catch (error) {
      debugPrint('Google Disconnect Error: $error');
    }
  }
}

/// Social authentication result
class SocialAuthResult {
  final bool success;
  final String? accessToken;
  final String? errorMessage;
  final SocialAuthProvider? provider;

  SocialAuthResult({
    required this.success,
    this.accessToken,
    this.errorMessage,
    this.provider,
  });

  factory SocialAuthResult.success({
    required String accessToken,
    required SocialAuthProvider provider,
  }) {
    return SocialAuthResult(
      success: true,
      accessToken: accessToken,
      provider: provider,
    );
  }

  factory SocialAuthResult.failure({
    required String errorMessage,
    SocialAuthProvider? provider,
  }) {
    return SocialAuthResult(
      success: false,
      errorMessage: errorMessage,
      provider: provider,
    );
  }

  factory SocialAuthResult.cancelled({
    SocialAuthProvider? provider,
  }) {
    return SocialAuthResult(
      success: false,
      errorMessage: 'User cancelled authentication',
      provider: provider,
    );
  }
}

/// Social authentication providers
enum SocialAuthProvider {
  google,
  apple,
}

extension SocialAuthProviderExtension on SocialAuthProvider {
  String get name {
    switch (this) {
      case SocialAuthProvider.google:
        return 'Google';
      case SocialAuthProvider.apple:
        return 'Apple';
    }
  }

  String get displayName {
    switch (this) {
      case SocialAuthProvider.google:
        return 'Continue with Google';
      case SocialAuthProvider.apple:
        return 'Continue with Apple';
    }
  }
}
